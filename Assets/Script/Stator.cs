using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

public class Stator : MonoBehaviour
{
    public Vector3 Direction { get; set; } = Vector3.zero; // 初始移动方向
    public float Width { get; set; } //导轨的宽度

    public float Pos { get; private set; } ///当前导轨相对左或右定位点的位置

    // void Start()
    // {
    //     StartCoroutine(UpdateTransform()); // 启动协程
    // }

    // IEnumerator UpdateTransform()
    // {
    //     yield return new WaitForEndOfFrameUnit(); // 等待一帧结束
    //     var line = GetComponentInParent<Line>();
    //     if (line.name == "LineLeft" || line.name == "LineRight")
    //     {
    //         direction = Vector3.up;
    //         var localPos = direction * Pos;
    //         transform.localPosition = localPos;
    //     }
    //     yield return new WaitForSeconds(0.01f); // 等待0.1秒
    // }

    // Update is called once per frame
    // void Update()
    // {
    //     transform.localPosition = Direction * Pos;
    // }



    public void SetPos(float realPos)
    {
        var pos = (float)(realPos < 30 ? 0 : realPos);
        Pos = pos;
    }


}
