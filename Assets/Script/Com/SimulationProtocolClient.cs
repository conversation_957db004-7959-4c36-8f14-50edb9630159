using System.Collections;
using System.Collections.Generic;
using Grpc.Core;
using GRpc.SimulationProtocol;
using System.Threading.Tasks;
using Google.Protobuf.WellKnownTypes;
using System.Runtime.CompilerServices;
using System.Threading;
using System;
using System.Linq;
using UnityEngine;

public class SimulationProtocolClient
{
    private readonly SimulationProtocolService.SimulationProtocolServiceClient _client;
    private readonly Channel _channel;
    private readonly string _server;
    private readonly CancellationTokenSource _cts;

    public bool IsBusy { get; private set; }

    public AxisFeedbackList AxisFeedbackList { get; private set; }

    public RoAxisFeedbackList RoAxisFeedbackList { get; private set; }

    public SimulationProtocolClient(string host, string port)
    {
        _server = host + ":" + port;
        _channel = new Channel(_server, ChannelCredentials.Insecure);
        _client = new SimulationProtocolService.SimulationProtocolServiceClient(_channel);
        _cts = new CancellationTokenSource();
    }


    public async Task GetAxisFeedbacksAsync()
    {
        var call = _client.GetAxisFeedbacks(new(), cancellationToken: _cts.Token);
        try
        {
            while (await call.ResponseStream.MoveNext())
            {
                IsBusy = true;
                AxisFeedbackList = call.ResponseStream.Current;
            }
        }
        finally
        {
            IsBusy = false;
        }
    }

    public async Task GetRoAxisFeedbacksAsync()
    {
        var call = _client.GetRoAxisFeedbacks(new(), cancellationToken: _cts.Token);
        try
        {
            while (await call.ResponseStream.MoveNext())
            {
                IsBusy = true;
                RoAxisFeedbackList = call.ResponseStream.Current;
            }
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// Get the list of station configurations from the server.
    /// </summary>
    /// <returns></returns>
    public List<StationConfig> GetWorkstationConfigs()
    {
        var callOptions = new CallOptions(deadline: DateTime.UtcNow.AddSeconds(1));
        var call = _client.GetStationConfigs(new(), callOptions);
        return call.StationConfigs.ToList();
    }

    /// <summary>
    /// Get the list of view line configurations from the server.
    /// </summary>
    /// <returns></returns>
    public List<ViewLineConfig> GetViewLineConfigs()
    {
        var callOptions = new CallOptions(deadline: DateTime.UtcNow.AddSeconds(1));
        var call = _client.GetViewLineConfigs(new(), callOptions);
        return call.ViewLineConfigs.ToList();
    }


    public bool IsReady()
    {
        return _channel.State is ChannelState.Ready;
    }


    public async void OnDisable()
    {
        _cts.Cancel();
        await _channel.ShutdownAsync();
    }
}