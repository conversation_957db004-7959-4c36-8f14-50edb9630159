using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Grpc.Core;
using System.Threading.Tasks;
using System;
using GRpc.SimulationProtocol;

public class ComClientManager : MonoBehaviour
{
    private string gRpcHost = "127.0.0.1";
    private string gRpcPort = "11111";

    private SimulationProtocolClient simClient;

    public event Action<AxisFeedbackList> OnAxisFeedbackReceived;

    public event Action<RoAxisFeedbackList> OnRoAxisFeedbackReceived;

    public static ComClientManager Instance { get; private set; }

    private void Awake()
    {
        Instance = this;
        simClient = new SimulationProtocolClient(gRpcHost, gRpcPort);
        // ViewManager.Instance.lineConfig = simClient.GetLineConfig();
        ViewManager.Instance.ViewLineConfigs = simClient.GetViewLineConfigs();
        ViewManager.Instance.StationConfigs = simClient.GetWorkstationConfigs();
    }

    void Start()
    {
        ConnectToGrpcClient();
        StartCoroutine(AxisFeedbackRefresh(true));
        StartCoroutine(RoAxisFeedbackRefresh(true));
    }

    private void ConnectToGrpcClient()
    {
        try
        {
            Task.Factory.StartNew(() => { simClient.GetAxisFeedbacksAsync().ConfigureAwait(false); },
                TaskCreationOptions.LongRunning);

            Task.Factory.StartNew(() => { simClient.GetRoAxisFeedbacksAsync().ConfigureAwait(false); },
                TaskCreationOptions.LongRunning);


        }
        catch (Exception ex)
        {
            Debug.LogError($"Error while receiving axis feedback: {ex.Message}");
        }
    }


    /// <summary>
    /// 反馈信息刷新到UI
    /// </summary>
    /// <param name="async"></param>
    /// <returns></returns>
    IEnumerator AxisFeedbackRefresh(bool async = false)
    {
        while (true)
        {
            yield return new WaitUntil(() => simClient.AxisFeedbackList != null); //等待赋值
            if (async)
            {
                UnityMainThreadDispatcher.Instance()
                    .Enqueue(() => OnAxisFeedbackReceived?.Invoke(simClient.AxisFeedbackList));
            }
            else
            {
                OnAxisFeedbackReceived?.Invoke(simClient.AxisFeedbackList);
            }

            yield return new WaitForSeconds(0.016f);//每16ms刷新一次，1秒60帧
        }
    }

    IEnumerator RoAxisFeedbackRefresh(bool async = false)
    {
        while (true)
        {
            yield return new WaitUntil(() => simClient.RoAxisFeedbackList != null); //等待赋值
            if (async)
            {
                UnityMainThreadDispatcher.Instance()
                    .Enqueue(() => OnRoAxisFeedbackReceived?.Invoke(simClient.RoAxisFeedbackList));
            }
            else
            {
                OnRoAxisFeedbackReceived?.Invoke(simClient.RoAxisFeedbackList);
            }

            yield return new WaitForSeconds(0.016f);
        }
    }

    void OnDestroy()
    {
        simClient?.OnDisable();
    }
}