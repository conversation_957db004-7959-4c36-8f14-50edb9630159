// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: SimulationProtocol.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace GRpc.SimulationProtocol {

  /// <summary>Holder for reflection information generated from SimulationProtocol.proto</summary>
  public static partial class SimulationProtocolReflection {

    #region Descriptor
    /// <summary>File descriptor for SimulationProtocol.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static SimulationProtocolReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChhTaW11bGF0aW9uUHJvdG9jb2wucHJvdG8SElNpbXVsYXRpb25Qcm90b2Nv",
            "bBobZ29vZ2xlL3Byb3RvYnVmL2VtcHR5LnByb3RvIs0BCgxBeGlzRmVlZGJh",
            "Y2sSDwoHSUF4aXNJRBgBIAEoBRIWCg5JQXhpc0N1ck9iamVjdBgCIAEoERIY",
            "ChBJQXhpc0N1ck9iamVjdElEGAMgASgREhgKEFVpQXhpc0RydkVyckNvZGUY",
            "BCABKA0SGwoTVWlBeGlzTW90aW9uRXJyQ29kZRgFIAEoDRIUCgxEaUF4aXND",
            "dXJQb3MYBiABKAUSFAoMRGlBeGlzQ3VyVmVsGAcgASgFEhcKD1VkaUF4aXNS",
            "dW5TdGF0ZRgIIAEoDSKMAQoOUm9BeGlzRmVlZGJhY2sSEAoIQlBvd2Vyb24Y",
            "ASABKAgSEAoIQlJ1bm5pbmcYAiABKAgSEQoJQkhvbWVEb25lGAMgASgIEhUK",
            "DUR3QXhpc0Vycm9ySUQYBCABKA0SFQoNTHJBY3RWZWxvY2l0eRgFIAEoARIV",
            "Cg1MckFjdFBvc2l0aW9uGAYgASgBIksKEEF4aXNGZWVkYmFja0xpc3QSNwoN",
            "YXhpc0ZlZWRiYWNrcxgBIAMoCzIgLlNpbXVsYXRpb25Qcm90b2NvbC5BeGlz",
            "RmVlZGJhY2siUQoSUm9BeGlzRmVlZGJhY2tMaXN0EjsKD3JvQXhpc0ZlZWRi",
            "YWNrcxgBIAMoCzIiLlNpbXVsYXRpb25Qcm90b2NvbC5Sb0F4aXNGZWVkYmFj",
            "ayJvCg1TdGF0aW9uQ29uZmlnEg4KBklPbk9iahgBIAEoBRIQCghJT25PYmpJ",
            "RBgCIAEoBRISCgpJU3RhdGlvbklEGAMgASgFEhIKCklTdG5FbmFibGUYBCAB",
            "KAUSFAoMTHJTdGF0aW9uUG9zGAUgASgBIk4KEVN0YXRpb25Db25maWdMaXN0",
            "EjkKDnN0YXRpb25Db25maWdzGAEgAygLMiEuU2ltdWxhdGlvblByb3RvY29s",
            "LlN0YXRpb25Db25maWci1QIKDlZpZXdMaW5lQ29uZmlnEg8KB2xpbmVfaWQY",
            "ASABKAUSDAoEbmFtZRgCIAEoCRITCgtwcmVmYWJfbmFtZRgDIAEoCRIUCgxh",
            "bmNob3JfcG9pbnQYBCABKAkSFgoObW92ZV9kaXJlY3Rpb24YBSABKAUSFQoN",
            "c3RhdG9yX2xlbmd0aBgGIAEoAhIUCgxzdGF0b3JfY291bnQYByABKAUSFAoM",
            "aXNfcmVtb3ZhYmxlGAggASgIEg4KBmlzX2FyYxgJIAEoCBIRCgl0cmFuc3Bf",
            "aWQYCiABKAUSFQoNdHJhbnNwX2xlbmd0aBgLIAEoAhIZChFyb19heGlzX2Rp",
            "cmVjdGlvbhgMIAEoBRIZChF0cmFuc3BfcnVuX3RyYXZlbBgNIAEoAhISCgpy",
            "b19heGlzX2lkGA4gASgFEhoKEnJvX2F4aXNfcnVuX3JhZGl1cxgPIAEoAiJR",
            "ChJWaWV3TGluZUNvbmZpZ0xpc3QSOwoPdmlld0xpbmVDb25maWdzGAEgAygL",
            "MiIuU2ltdWxhdGlvblByb3RvY29sLlZpZXdMaW5lQ29uZmlnMvECChlTaW11",
            "bGF0aW9uUHJvdG9jb2xTZXJ2aWNlElIKEEdldEF4aXNGZWVkYmFja3MSFi5n",
            "b29nbGUucHJvdG9idWYuRW1wdHkaJC5TaW11bGF0aW9uUHJvdG9jb2wuQXhp",
            "c0ZlZWRiYWNrTGlzdDABElYKEkdldFJvQXhpc0ZlZWRiYWNrcxIWLmdvb2ds",
            "ZS5wcm90b2J1Zi5FbXB0eRomLlNpbXVsYXRpb25Qcm90b2NvbC5Sb0F4aXNG",
            "ZWVkYmFja0xpc3QwARJSChFHZXRTdGF0aW9uQ29uZmlncxIWLmdvb2dsZS5w",
            "cm90b2J1Zi5FbXB0eRolLlNpbXVsYXRpb25Qcm90b2NvbC5TdGF0aW9uQ29u",
            "ZmlnTGlzdBJUChJHZXRWaWV3TGluZUNvbmZpZ3MSFi5nb29nbGUucHJvdG9i",
            "dWYuRW1wdHkaJi5TaW11bGF0aW9uUHJvdG9jb2wuVmlld0xpbmVDb25maWdM",
            "aXN0QhqqAhdHUnBjLlNpbXVsYXRpb25Qcm90b2NvbGIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.WellKnownTypes.EmptyReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.AxisFeedback), global::GRpc.SimulationProtocol.AxisFeedback.Parser, new[]{ "IAxisID", "IAxisCurObject", "IAxisCurObjectID", "UiAxisDrvErrCode", "UiAxisMotionErrCode", "DiAxisCurPos", "DiAxisCurVel", "UdiAxisRunState" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.RoAxisFeedback), global::GRpc.SimulationProtocol.RoAxisFeedback.Parser, new[]{ "BPoweron", "BRunning", "BHomeDone", "DwAxisErrorID", "LrActVelocity", "LrActPosition" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.AxisFeedbackList), global::GRpc.SimulationProtocol.AxisFeedbackList.Parser, new[]{ "AxisFeedbacks" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.RoAxisFeedbackList), global::GRpc.SimulationProtocol.RoAxisFeedbackList.Parser, new[]{ "RoAxisFeedbacks" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.StationConfig), global::GRpc.SimulationProtocol.StationConfig.Parser, new[]{ "IOnObj", "IOnObjID", "IStationID", "IStnEnable", "LrStationPos" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.StationConfigList), global::GRpc.SimulationProtocol.StationConfigList.Parser, new[]{ "StationConfigs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.ViewLineConfig), global::GRpc.SimulationProtocol.ViewLineConfig.Parser, new[]{ "LineId", "Name", "PrefabName", "AnchorPoint", "MoveDirection", "StatorLength", "StatorCount", "IsRemovable", "IsArc", "TranspId", "TranspLength", "RoAxisDirection", "TranspRunTravel", "RoAxisId", "RoAxisRunRadius" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::GRpc.SimulationProtocol.ViewLineConfigList), global::GRpc.SimulationProtocol.ViewLineConfigList.Parser, new[]{ "ViewLineConfigs" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// 定义AxisFeedback消息格式
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AxisFeedback : pb::IMessage<AxisFeedback>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AxisFeedback> _parser = new pb::MessageParser<AxisFeedback>(() => new AxisFeedback());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AxisFeedback> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AxisFeedback() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AxisFeedback(AxisFeedback other) : this() {
      iAxisID_ = other.iAxisID_;
      iAxisCurObject_ = other.iAxisCurObject_;
      iAxisCurObjectID_ = other.iAxisCurObjectID_;
      uiAxisDrvErrCode_ = other.uiAxisDrvErrCode_;
      uiAxisMotionErrCode_ = other.uiAxisMotionErrCode_;
      diAxisCurPos_ = other.diAxisCurPos_;
      diAxisCurVel_ = other.diAxisCurVel_;
      udiAxisRunState_ = other.udiAxisRunState_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AxisFeedback Clone() {
      return new AxisFeedback(this);
    }

    /// <summary>Field number for the "IAxisID" field.</summary>
    public const int IAxisIDFieldNumber = 1;
    private int iAxisID_;
    /// <summary>
    /// 轴ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IAxisID {
      get { return iAxisID_; }
      set {
        iAxisID_ = value;
      }
    }

    /// <summary>Field number for the "IAxisCurObject" field.</summary>
    public const int IAxisCurObjectFieldNumber = 2;
    private int iAxisCurObject_;
    /// <summary>
    /// 动子所处对象，0：直线段 1：圆弧段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IAxisCurObject {
      get { return iAxisCurObject_; }
      set {
        iAxisCurObject_ = value;
      }
    }

    /// <summary>Field number for the "IAxisCurObjectID" field.</summary>
    public const int IAxisCurObjectIDFieldNumber = 3;
    private int iAxisCurObjectID_;
    /// <summary>
    /// 动子所处对象ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IAxisCurObjectID {
      get { return iAxisCurObjectID_; }
      set {
        iAxisCurObjectID_ = value;
      }
    }

    /// <summary>Field number for the "UiAxisDrvErrCode" field.</summary>
    public const int UiAxisDrvErrCodeFieldNumber = 4;
    private uint uiAxisDrvErrCode_;
    /// <summary>
    /// 轴驱动错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint UiAxisDrvErrCode {
      get { return uiAxisDrvErrCode_; }
      set {
        uiAxisDrvErrCode_ = value;
      }
    }

    /// <summary>Field number for the "UiAxisMotionErrCode" field.</summary>
    public const int UiAxisMotionErrCodeFieldNumber = 5;
    private uint uiAxisMotionErrCode_;
    /// <summary>
    /// 轴运动规划错误代码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint UiAxisMotionErrCode {
      get { return uiAxisMotionErrCode_; }
      set {
        uiAxisMotionErrCode_ = value;
      }
    }

    /// <summary>Field number for the "DiAxisCurPos" field.</summary>
    public const int DiAxisCurPosFieldNumber = 6;
    private int diAxisCurPos_;
    /// <summary>
    /// 轴当前位置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DiAxisCurPos {
      get { return diAxisCurPos_; }
      set {
        diAxisCurPos_ = value;
      }
    }

    /// <summary>Field number for the "DiAxisCurVel" field.</summary>
    public const int DiAxisCurVelFieldNumber = 7;
    private int diAxisCurVel_;
    /// <summary>
    /// 轴当前速度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DiAxisCurVel {
      get { return diAxisCurVel_; }
      set {
        diAxisCurVel_ = value;
      }
    }

    /// <summary>Field number for the "UdiAxisRunState" field.</summary>
    public const int UdiAxisRunStateFieldNumber = 8;
    private uint udiAxisRunState_;
    /// <summary>
    /// 轴运行状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint UdiAxisRunState {
      get { return udiAxisRunState_; }
      set {
        udiAxisRunState_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AxisFeedback);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AxisFeedback other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (IAxisID != other.IAxisID) return false;
      if (IAxisCurObject != other.IAxisCurObject) return false;
      if (IAxisCurObjectID != other.IAxisCurObjectID) return false;
      if (UiAxisDrvErrCode != other.UiAxisDrvErrCode) return false;
      if (UiAxisMotionErrCode != other.UiAxisMotionErrCode) return false;
      if (DiAxisCurPos != other.DiAxisCurPos) return false;
      if (DiAxisCurVel != other.DiAxisCurVel) return false;
      if (UdiAxisRunState != other.UdiAxisRunState) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (IAxisID != 0) hash ^= IAxisID.GetHashCode();
      if (IAxisCurObject != 0) hash ^= IAxisCurObject.GetHashCode();
      if (IAxisCurObjectID != 0) hash ^= IAxisCurObjectID.GetHashCode();
      if (UiAxisDrvErrCode != 0) hash ^= UiAxisDrvErrCode.GetHashCode();
      if (UiAxisMotionErrCode != 0) hash ^= UiAxisMotionErrCode.GetHashCode();
      if (DiAxisCurPos != 0) hash ^= DiAxisCurPos.GetHashCode();
      if (DiAxisCurVel != 0) hash ^= DiAxisCurVel.GetHashCode();
      if (UdiAxisRunState != 0) hash ^= UdiAxisRunState.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (IAxisID != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(IAxisID);
      }
      if (IAxisCurObject != 0) {
        output.WriteRawTag(16);
        output.WriteSInt32(IAxisCurObject);
      }
      if (IAxisCurObjectID != 0) {
        output.WriteRawTag(24);
        output.WriteSInt32(IAxisCurObjectID);
      }
      if (UiAxisDrvErrCode != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(UiAxisDrvErrCode);
      }
      if (UiAxisMotionErrCode != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(UiAxisMotionErrCode);
      }
      if (DiAxisCurPos != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(DiAxisCurPos);
      }
      if (DiAxisCurVel != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(DiAxisCurVel);
      }
      if (UdiAxisRunState != 0) {
        output.WriteRawTag(64);
        output.WriteUInt32(UdiAxisRunState);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (IAxisID != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(IAxisID);
      }
      if (IAxisCurObject != 0) {
        output.WriteRawTag(16);
        output.WriteSInt32(IAxisCurObject);
      }
      if (IAxisCurObjectID != 0) {
        output.WriteRawTag(24);
        output.WriteSInt32(IAxisCurObjectID);
      }
      if (UiAxisDrvErrCode != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(UiAxisDrvErrCode);
      }
      if (UiAxisMotionErrCode != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(UiAxisMotionErrCode);
      }
      if (DiAxisCurPos != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(DiAxisCurPos);
      }
      if (DiAxisCurVel != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(DiAxisCurVel);
      }
      if (UdiAxisRunState != 0) {
        output.WriteRawTag(64);
        output.WriteUInt32(UdiAxisRunState);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (IAxisID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(IAxisID);
      }
      if (IAxisCurObject != 0) {
        size += 1 + pb::CodedOutputStream.ComputeSInt32Size(IAxisCurObject);
      }
      if (IAxisCurObjectID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeSInt32Size(IAxisCurObjectID);
      }
      if (UiAxisDrvErrCode != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(UiAxisDrvErrCode);
      }
      if (UiAxisMotionErrCode != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(UiAxisMotionErrCode);
      }
      if (DiAxisCurPos != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DiAxisCurPos);
      }
      if (DiAxisCurVel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DiAxisCurVel);
      }
      if (UdiAxisRunState != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(UdiAxisRunState);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AxisFeedback other) {
      if (other == null) {
        return;
      }
      if (other.IAxisID != 0) {
        IAxisID = other.IAxisID;
      }
      if (other.IAxisCurObject != 0) {
        IAxisCurObject = other.IAxisCurObject;
      }
      if (other.IAxisCurObjectID != 0) {
        IAxisCurObjectID = other.IAxisCurObjectID;
      }
      if (other.UiAxisDrvErrCode != 0) {
        UiAxisDrvErrCode = other.UiAxisDrvErrCode;
      }
      if (other.UiAxisMotionErrCode != 0) {
        UiAxisMotionErrCode = other.UiAxisMotionErrCode;
      }
      if (other.DiAxisCurPos != 0) {
        DiAxisCurPos = other.DiAxisCurPos;
      }
      if (other.DiAxisCurVel != 0) {
        DiAxisCurVel = other.DiAxisCurVel;
      }
      if (other.UdiAxisRunState != 0) {
        UdiAxisRunState = other.UdiAxisRunState;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            IAxisID = input.ReadInt32();
            break;
          }
          case 16: {
            IAxisCurObject = input.ReadSInt32();
            break;
          }
          case 24: {
            IAxisCurObjectID = input.ReadSInt32();
            break;
          }
          case 32: {
            UiAxisDrvErrCode = input.ReadUInt32();
            break;
          }
          case 40: {
            UiAxisMotionErrCode = input.ReadUInt32();
            break;
          }
          case 48: {
            DiAxisCurPos = input.ReadInt32();
            break;
          }
          case 56: {
            DiAxisCurVel = input.ReadInt32();
            break;
          }
          case 64: {
            UdiAxisRunState = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            IAxisID = input.ReadInt32();
            break;
          }
          case 16: {
            IAxisCurObject = input.ReadSInt32();
            break;
          }
          case 24: {
            IAxisCurObjectID = input.ReadSInt32();
            break;
          }
          case 32: {
            UiAxisDrvErrCode = input.ReadUInt32();
            break;
          }
          case 40: {
            UiAxisMotionErrCode = input.ReadUInt32();
            break;
          }
          case 48: {
            DiAxisCurPos = input.ReadInt32();
            break;
          }
          case 56: {
            DiAxisCurVel = input.ReadInt32();
            break;
          }
          case 64: {
            UdiAxisRunState = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoAxisFeedback : pb::IMessage<RoAxisFeedback>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoAxisFeedback> _parser = new pb::MessageParser<RoAxisFeedback>(() => new RoAxisFeedback());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoAxisFeedback> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoAxisFeedback() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoAxisFeedback(RoAxisFeedback other) : this() {
      bPoweron_ = other.bPoweron_;
      bRunning_ = other.bRunning_;
      bHomeDone_ = other.bHomeDone_;
      dwAxisErrorID_ = other.dwAxisErrorID_;
      lrActVelocity_ = other.lrActVelocity_;
      lrActPosition_ = other.lrActPosition_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoAxisFeedback Clone() {
      return new RoAxisFeedback(this);
    }

    /// <summary>Field number for the "BPoweron" field.</summary>
    public const int BPoweronFieldNumber = 1;
    private bool bPoweron_;
    /// <summary>
    /// 轴的使能状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool BPoweron {
      get { return bPoweron_; }
      set {
        bPoweron_ = value;
      }
    }

    /// <summary>Field number for the "BRunning" field.</summary>
    public const int BRunningFieldNumber = 2;
    private bool bRunning_;
    /// <summary>
    /// 运行状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool BRunning {
      get { return bRunning_; }
      set {
        bRunning_ = value;
      }
    }

    /// <summary>Field number for the "BHomeDone" field.</summary>
    public const int BHomeDoneFieldNumber = 3;
    private bool bHomeDone_;
    /// <summary>
    /// 回零完成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool BHomeDone {
      get { return bHomeDone_; }
      set {
        bHomeDone_ = value;
      }
    }

    /// <summary>Field number for the "DwAxisErrorID" field.</summary>
    public const int DwAxisErrorIDFieldNumber = 4;
    private uint dwAxisErrorID_;
    /// <summary>
    /// 轴的错误码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint DwAxisErrorID {
      get { return dwAxisErrorID_; }
      set {
        dwAxisErrorID_ = value;
      }
    }

    /// <summary>Field number for the "LrActVelocity" field.</summary>
    public const int LrActVelocityFieldNumber = 5;
    private double lrActVelocity_;
    /// <summary>
    /// 轴的实际速度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double LrActVelocity {
      get { return lrActVelocity_; }
      set {
        lrActVelocity_ = value;
      }
    }

    /// <summary>Field number for the "LrActPosition" field.</summary>
    public const int LrActPositionFieldNumber = 6;
    private double lrActPosition_;
    /// <summary>
    /// 轴的实际位置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double LrActPosition {
      get { return lrActPosition_; }
      set {
        lrActPosition_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoAxisFeedback);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoAxisFeedback other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BPoweron != other.BPoweron) return false;
      if (BRunning != other.BRunning) return false;
      if (BHomeDone != other.BHomeDone) return false;
      if (DwAxisErrorID != other.DwAxisErrorID) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(LrActVelocity, other.LrActVelocity)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(LrActPosition, other.LrActPosition)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BPoweron != false) hash ^= BPoweron.GetHashCode();
      if (BRunning != false) hash ^= BRunning.GetHashCode();
      if (BHomeDone != false) hash ^= BHomeDone.GetHashCode();
      if (DwAxisErrorID != 0) hash ^= DwAxisErrorID.GetHashCode();
      if (LrActVelocity != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(LrActVelocity);
      if (LrActPosition != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(LrActPosition);
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BPoweron != false) {
        output.WriteRawTag(8);
        output.WriteBool(BPoweron);
      }
      if (BRunning != false) {
        output.WriteRawTag(16);
        output.WriteBool(BRunning);
      }
      if (BHomeDone != false) {
        output.WriteRawTag(24);
        output.WriteBool(BHomeDone);
      }
      if (DwAxisErrorID != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(DwAxisErrorID);
      }
      if (LrActVelocity != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(LrActVelocity);
      }
      if (LrActPosition != 0D) {
        output.WriteRawTag(49);
        output.WriteDouble(LrActPosition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BPoweron != false) {
        output.WriteRawTag(8);
        output.WriteBool(BPoweron);
      }
      if (BRunning != false) {
        output.WriteRawTag(16);
        output.WriteBool(BRunning);
      }
      if (BHomeDone != false) {
        output.WriteRawTag(24);
        output.WriteBool(BHomeDone);
      }
      if (DwAxisErrorID != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(DwAxisErrorID);
      }
      if (LrActVelocity != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(LrActVelocity);
      }
      if (LrActPosition != 0D) {
        output.WriteRawTag(49);
        output.WriteDouble(LrActPosition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BPoweron != false) {
        size += 1 + 1;
      }
      if (BRunning != false) {
        size += 1 + 1;
      }
      if (BHomeDone != false) {
        size += 1 + 1;
      }
      if (DwAxisErrorID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(DwAxisErrorID);
      }
      if (LrActVelocity != 0D) {
        size += 1 + 8;
      }
      if (LrActPosition != 0D) {
        size += 1 + 8;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoAxisFeedback other) {
      if (other == null) {
        return;
      }
      if (other.BPoweron != false) {
        BPoweron = other.BPoweron;
      }
      if (other.BRunning != false) {
        BRunning = other.BRunning;
      }
      if (other.BHomeDone != false) {
        BHomeDone = other.BHomeDone;
      }
      if (other.DwAxisErrorID != 0) {
        DwAxisErrorID = other.DwAxisErrorID;
      }
      if (other.LrActVelocity != 0D) {
        LrActVelocity = other.LrActVelocity;
      }
      if (other.LrActPosition != 0D) {
        LrActPosition = other.LrActPosition;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BPoweron = input.ReadBool();
            break;
          }
          case 16: {
            BRunning = input.ReadBool();
            break;
          }
          case 24: {
            BHomeDone = input.ReadBool();
            break;
          }
          case 32: {
            DwAxisErrorID = input.ReadUInt32();
            break;
          }
          case 41: {
            LrActVelocity = input.ReadDouble();
            break;
          }
          case 49: {
            LrActPosition = input.ReadDouble();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BPoweron = input.ReadBool();
            break;
          }
          case 16: {
            BRunning = input.ReadBool();
            break;
          }
          case 24: {
            BHomeDone = input.ReadBool();
            break;
          }
          case 32: {
            DwAxisErrorID = input.ReadUInt32();
            break;
          }
          case 41: {
            LrActVelocity = input.ReadDouble();
            break;
          }
          case 49: {
            LrActPosition = input.ReadDouble();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 定义包含多个AxisFeedback消息的列表
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AxisFeedbackList : pb::IMessage<AxisFeedbackList>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AxisFeedbackList> _parser = new pb::MessageParser<AxisFeedbackList>(() => new AxisFeedbackList());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AxisFeedbackList> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AxisFeedbackList() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AxisFeedbackList(AxisFeedbackList other) : this() {
      axisFeedbacks_ = other.axisFeedbacks_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AxisFeedbackList Clone() {
      return new AxisFeedbackList(this);
    }

    /// <summary>Field number for the "axisFeedbacks" field.</summary>
    public const int AxisFeedbacksFieldNumber = 1;
    private static readonly pb::FieldCodec<global::GRpc.SimulationProtocol.AxisFeedback> _repeated_axisFeedbacks_codec
        = pb::FieldCodec.ForMessage(10, global::GRpc.SimulationProtocol.AxisFeedback.Parser);
    private readonly pbc::RepeatedField<global::GRpc.SimulationProtocol.AxisFeedback> axisFeedbacks_ = new pbc::RepeatedField<global::GRpc.SimulationProtocol.AxisFeedback>();
    /// <summary>
    /// 使用repeated关键字定义列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::GRpc.SimulationProtocol.AxisFeedback> AxisFeedbacks {
      get { return axisFeedbacks_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AxisFeedbackList);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AxisFeedbackList other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!axisFeedbacks_.Equals(other.axisFeedbacks_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= axisFeedbacks_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      axisFeedbacks_.WriteTo(output, _repeated_axisFeedbacks_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      axisFeedbacks_.WriteTo(ref output, _repeated_axisFeedbacks_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += axisFeedbacks_.CalculateSize(_repeated_axisFeedbacks_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AxisFeedbackList other) {
      if (other == null) {
        return;
      }
      axisFeedbacks_.Add(other.axisFeedbacks_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            axisFeedbacks_.AddEntriesFrom(input, _repeated_axisFeedbacks_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            axisFeedbacks_.AddEntriesFrom(ref input, _repeated_axisFeedbacks_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RoAxisFeedbackList : pb::IMessage<RoAxisFeedbackList>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RoAxisFeedbackList> _parser = new pb::MessageParser<RoAxisFeedbackList>(() => new RoAxisFeedbackList());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RoAxisFeedbackList> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoAxisFeedbackList() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoAxisFeedbackList(RoAxisFeedbackList other) : this() {
      roAxisFeedbacks_ = other.roAxisFeedbacks_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RoAxisFeedbackList Clone() {
      return new RoAxisFeedbackList(this);
    }

    /// <summary>Field number for the "roAxisFeedbacks" field.</summary>
    public const int RoAxisFeedbacksFieldNumber = 1;
    private static readonly pb::FieldCodec<global::GRpc.SimulationProtocol.RoAxisFeedback> _repeated_roAxisFeedbacks_codec
        = pb::FieldCodec.ForMessage(10, global::GRpc.SimulationProtocol.RoAxisFeedback.Parser);
    private readonly pbc::RepeatedField<global::GRpc.SimulationProtocol.RoAxisFeedback> roAxisFeedbacks_ = new pbc::RepeatedField<global::GRpc.SimulationProtocol.RoAxisFeedback>();
    /// <summary>
    /// 使用repeated关键字定义列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::GRpc.SimulationProtocol.RoAxisFeedback> RoAxisFeedbacks {
      get { return roAxisFeedbacks_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RoAxisFeedbackList);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RoAxisFeedbackList other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!roAxisFeedbacks_.Equals(other.roAxisFeedbacks_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= roAxisFeedbacks_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      roAxisFeedbacks_.WriteTo(output, _repeated_roAxisFeedbacks_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      roAxisFeedbacks_.WriteTo(ref output, _repeated_roAxisFeedbacks_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += roAxisFeedbacks_.CalculateSize(_repeated_roAxisFeedbacks_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RoAxisFeedbackList other) {
      if (other == null) {
        return;
      }
      roAxisFeedbacks_.Add(other.roAxisFeedbacks_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            roAxisFeedbacks_.AddEntriesFrom(input, _repeated_roAxisFeedbacks_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            roAxisFeedbacks_.AddEntriesFrom(ref input, _repeated_roAxisFeedbacks_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class StationConfig : pb::IMessage<StationConfig>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<StationConfig> _parser = new pb::MessageParser<StationConfig>(() => new StationConfig());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<StationConfig> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StationConfig() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StationConfig(StationConfig other) : this() {
      iOnObj_ = other.iOnObj_;
      iOnObjID_ = other.iOnObjID_;
      iStationID_ = other.iStationID_;
      iStnEnable_ = other.iStnEnable_;
      lrStationPos_ = other.lrStationPos_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StationConfig Clone() {
      return new StationConfig(this);
    }

    /// <summary>Field number for the "IOnObj" field.</summary>
    public const int IOnObjFieldNumber = 1;
    private int iOnObj_;
    /// <summary>
    ///所在对象类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IOnObj {
      get { return iOnObj_; }
      set {
        iOnObj_ = value;
      }
    }

    /// <summary>Field number for the "IOnObjID" field.</summary>
    public const int IOnObjIDFieldNumber = 2;
    private int iOnObjID_;
    /// <summary>
    ///所在对象ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IOnObjID {
      get { return iOnObjID_; }
      set {
        iOnObjID_ = value;
      }
    }

    /// <summary>Field number for the "IStationID" field.</summary>
    public const int IStationIDFieldNumber = 3;
    private int iStationID_;
    /// <summary>
    ///工位ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IStationID {
      get { return iStationID_; }
      set {
        iStationID_ = value;
      }
    }

    /// <summary>Field number for the "IStnEnable" field.</summary>
    public const int IStnEnableFieldNumber = 4;
    private int iStnEnable_;
    /// <summary>
    ///启用
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int IStnEnable {
      get { return iStnEnable_; }
      set {
        iStnEnable_ = value;
      }
    }

    /// <summary>Field number for the "LrStationPos" field.</summary>
    public const int LrStationPosFieldNumber = 5;
    private double lrStationPos_;
    /// <summary>
    ///工位位置
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double LrStationPos {
      get { return lrStationPos_; }
      set {
        lrStationPos_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as StationConfig);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(StationConfig other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (IOnObj != other.IOnObj) return false;
      if (IOnObjID != other.IOnObjID) return false;
      if (IStationID != other.IStationID) return false;
      if (IStnEnable != other.IStnEnable) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(LrStationPos, other.LrStationPos)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (IOnObj != 0) hash ^= IOnObj.GetHashCode();
      if (IOnObjID != 0) hash ^= IOnObjID.GetHashCode();
      if (IStationID != 0) hash ^= IStationID.GetHashCode();
      if (IStnEnable != 0) hash ^= IStnEnable.GetHashCode();
      if (LrStationPos != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(LrStationPos);
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (IOnObj != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(IOnObj);
      }
      if (IOnObjID != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(IOnObjID);
      }
      if (IStationID != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(IStationID);
      }
      if (IStnEnable != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(IStnEnable);
      }
      if (LrStationPos != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(LrStationPos);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (IOnObj != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(IOnObj);
      }
      if (IOnObjID != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(IOnObjID);
      }
      if (IStationID != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(IStationID);
      }
      if (IStnEnable != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(IStnEnable);
      }
      if (LrStationPos != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(LrStationPos);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (IOnObj != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(IOnObj);
      }
      if (IOnObjID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(IOnObjID);
      }
      if (IStationID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(IStationID);
      }
      if (IStnEnable != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(IStnEnable);
      }
      if (LrStationPos != 0D) {
        size += 1 + 8;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(StationConfig other) {
      if (other == null) {
        return;
      }
      if (other.IOnObj != 0) {
        IOnObj = other.IOnObj;
      }
      if (other.IOnObjID != 0) {
        IOnObjID = other.IOnObjID;
      }
      if (other.IStationID != 0) {
        IStationID = other.IStationID;
      }
      if (other.IStnEnable != 0) {
        IStnEnable = other.IStnEnable;
      }
      if (other.LrStationPos != 0D) {
        LrStationPos = other.LrStationPos;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            IOnObj = input.ReadInt32();
            break;
          }
          case 16: {
            IOnObjID = input.ReadInt32();
            break;
          }
          case 24: {
            IStationID = input.ReadInt32();
            break;
          }
          case 32: {
            IStnEnable = input.ReadInt32();
            break;
          }
          case 41: {
            LrStationPos = input.ReadDouble();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            IOnObj = input.ReadInt32();
            break;
          }
          case 16: {
            IOnObjID = input.ReadInt32();
            break;
          }
          case 24: {
            IStationID = input.ReadInt32();
            break;
          }
          case 32: {
            IStnEnable = input.ReadInt32();
            break;
          }
          case 41: {
            LrStationPos = input.ReadDouble();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class StationConfigList : pb::IMessage<StationConfigList>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<StationConfigList> _parser = new pb::MessageParser<StationConfigList>(() => new StationConfigList());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<StationConfigList> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StationConfigList() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StationConfigList(StationConfigList other) : this() {
      stationConfigs_ = other.stationConfigs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StationConfigList Clone() {
      return new StationConfigList(this);
    }

    /// <summary>Field number for the "stationConfigs" field.</summary>
    public const int StationConfigsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::GRpc.SimulationProtocol.StationConfig> _repeated_stationConfigs_codec
        = pb::FieldCodec.ForMessage(10, global::GRpc.SimulationProtocol.StationConfig.Parser);
    private readonly pbc::RepeatedField<global::GRpc.SimulationProtocol.StationConfig> stationConfigs_ = new pbc::RepeatedField<global::GRpc.SimulationProtocol.StationConfig>();
    /// <summary>
    /// 使用repeated关键字定义列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::GRpc.SimulationProtocol.StationConfig> StationConfigs {
      get { return stationConfigs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as StationConfigList);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(StationConfigList other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!stationConfigs_.Equals(other.stationConfigs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= stationConfigs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      stationConfigs_.WriteTo(output, _repeated_stationConfigs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      stationConfigs_.WriteTo(ref output, _repeated_stationConfigs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += stationConfigs_.CalculateSize(_repeated_stationConfigs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(StationConfigList other) {
      if (other == null) {
        return;
      }
      stationConfigs_.Add(other.stationConfigs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            stationConfigs_.AddEntriesFrom(input, _repeated_stationConfigs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            stationConfigs_.AddEntriesFrom(ref input, _repeated_stationConfigs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ViewLineConfig : pb::IMessage<ViewLineConfig>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ViewLineConfig> _parser = new pb::MessageParser<ViewLineConfig>(() => new ViewLineConfig());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ViewLineConfig> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ViewLineConfig() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ViewLineConfig(ViewLineConfig other) : this() {
      lineId_ = other.lineId_;
      name_ = other.name_;
      prefabName_ = other.prefabName_;
      anchorPoint_ = other.anchorPoint_;
      moveDirection_ = other.moveDirection_;
      statorLength_ = other.statorLength_;
      statorCount_ = other.statorCount_;
      isRemovable_ = other.isRemovable_;
      isArc_ = other.isArc_;
      transpId_ = other.transpId_;
      transpLength_ = other.transpLength_;
      roAxisDirection_ = other.roAxisDirection_;
      transpRunTravel_ = other.transpRunTravel_;
      roAxisId_ = other.roAxisId_;
      roAxisRunRadius_ = other.roAxisRunRadius_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ViewLineConfig Clone() {
      return new ViewLineConfig(this);
    }

    /// <summary>Field number for the "line_id" field.</summary>
    public const int LineIdFieldNumber = 1;
    private int lineId_;
    /// <summary>
    /// 线体ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int LineId {
      get { return lineId_; }
      set {
        lineId_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    /// <summary>
    /// 名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "prefab_name" field.</summary>
    public const int PrefabNameFieldNumber = 3;
    private string prefabName_ = "";
    /// <summary>
    /// 预制体名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string PrefabName {
      get { return prefabName_; }
      set {
        prefabName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "anchor_point" field.</summary>
    public const int AnchorPointFieldNumber = 4;
    private string anchorPoint_ = "";
    /// <summary>
    /// 定位点 (x,y,z)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string AnchorPoint {
      get { return anchorPoint_; }
      set {
        anchorPoint_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "move_direction" field.</summary>
    public const int MoveDirectionFieldNumber = 5;
    private int moveDirection_;
    /// <summary>
    /// 移动正方向 (1-4)上下左右
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int MoveDirection {
      get { return moveDirection_; }
      set {
        moveDirection_ = value;
      }
    }

    /// <summary>Field number for the "stator_length" field.</summary>
    public const int StatorLengthFieldNumber = 6;
    private float statorLength_;
    /// <summary>
    /// 定子长度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float StatorLength {
      get { return statorLength_; }
      set {
        statorLength_ = value;
      }
    }

    /// <summary>Field number for the "stator_count" field.</summary>
    public const int StatorCountFieldNumber = 7;
    private int statorCount_;
    /// <summary>
    /// 定子数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int StatorCount {
      get { return statorCount_; }
      set {
        statorCount_ = value;
      }
    }

    /// <summary>Field number for the "is_removable" field.</summary>
    public const int IsRemovableFieldNumber = 8;
    private bool isRemovable_;
    /// <summary>
    /// 是否可移动段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsRemovable {
      get { return isRemovable_; }
      set {
        isRemovable_ = value;
      }
    }

    /// <summary>Field number for the "is_arc" field.</summary>
    public const int IsArcFieldNumber = 9;
    private bool isArc_;
    /// <summary>
    /// 是否弧形段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsArc {
      get { return isArc_; }
      set {
        isArc_ = value;
      }
    }

    /// <summary>Field number for the "transp_id" field.</summary>
    public const int TranspIdFieldNumber = 10;
    private int transpId_;
    /// <summary>
    /// 接驳电机Id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int TranspId {
      get { return transpId_; }
      set {
        transpId_ = value;
      }
    }

    /// <summary>Field number for the "transp_length" field.</summary>
    public const int TranspLengthFieldNumber = 11;
    private float transpLength_;
    /// <summary>
    /// 接驳长度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float TranspLength {
      get { return transpLength_; }
      set {
        transpLength_ = value;
      }
    }

    /// <summary>Field number for the "ro_axis_direction" field.</summary>
    public const int RoAxisDirectionFieldNumber = 12;
    private int roAxisDirection_;
    /// <summary>
    /// 旋转轴移动正方向 (1-4)上下左右
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RoAxisDirection {
      get { return roAxisDirection_; }
      set {
        roAxisDirection_ = value;
      }
    }

    /// <summary>Field number for the "transp_run_travel" field.</summary>
    public const int TranspRunTravelFieldNumber = 13;
    private float transpRunTravel_;
    /// <summary>
    /// 控制器反馈的接驳电机最大行程
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float TranspRunTravel {
      get { return transpRunTravel_; }
      set {
        transpRunTravel_ = value;
      }
    }

    /// <summary>Field number for the "ro_axis_id" field.</summary>
    public const int RoAxisIdFieldNumber = 14;
    private int roAxisId_;
    /// <summary>
    /// 旋转轴Id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RoAxisId {
      get { return roAxisId_; }
      set {
        roAxisId_ = value;
      }
    }

    /// <summary>Field number for the "ro_axis_run_radius" field.</summary>
    public const int RoAxisRunRadiusFieldNumber = 15;
    private float roAxisRunRadius_;
    /// <summary>
    /// 弧形段旋转半径
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float RoAxisRunRadius {
      get { return roAxisRunRadius_; }
      set {
        roAxisRunRadius_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ViewLineConfig);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ViewLineConfig other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LineId != other.LineId) return false;
      if (Name != other.Name) return false;
      if (PrefabName != other.PrefabName) return false;
      if (AnchorPoint != other.AnchorPoint) return false;
      if (MoveDirection != other.MoveDirection) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(StatorLength, other.StatorLength)) return false;
      if (StatorCount != other.StatorCount) return false;
      if (IsRemovable != other.IsRemovable) return false;
      if (IsArc != other.IsArc) return false;
      if (TranspId != other.TranspId) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(TranspLength, other.TranspLength)) return false;
      if (RoAxisDirection != other.RoAxisDirection) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(TranspRunTravel, other.TranspRunTravel)) return false;
      if (RoAxisId != other.RoAxisId) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(RoAxisRunRadius, other.RoAxisRunRadius)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LineId != 0) hash ^= LineId.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (PrefabName.Length != 0) hash ^= PrefabName.GetHashCode();
      if (AnchorPoint.Length != 0) hash ^= AnchorPoint.GetHashCode();
      if (MoveDirection != 0) hash ^= MoveDirection.GetHashCode();
      if (StatorLength != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(StatorLength);
      if (StatorCount != 0) hash ^= StatorCount.GetHashCode();
      if (IsRemovable != false) hash ^= IsRemovable.GetHashCode();
      if (IsArc != false) hash ^= IsArc.GetHashCode();
      if (TranspId != 0) hash ^= TranspId.GetHashCode();
      if (TranspLength != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(TranspLength);
      if (RoAxisDirection != 0) hash ^= RoAxisDirection.GetHashCode();
      if (TranspRunTravel != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(TranspRunTravel);
      if (RoAxisId != 0) hash ^= RoAxisId.GetHashCode();
      if (RoAxisRunRadius != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(RoAxisRunRadius);
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LineId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(LineId);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (PrefabName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(PrefabName);
      }
      if (AnchorPoint.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(AnchorPoint);
      }
      if (MoveDirection != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(MoveDirection);
      }
      if (StatorLength != 0F) {
        output.WriteRawTag(53);
        output.WriteFloat(StatorLength);
      }
      if (StatorCount != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(StatorCount);
      }
      if (IsRemovable != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsRemovable);
      }
      if (IsArc != false) {
        output.WriteRawTag(72);
        output.WriteBool(IsArc);
      }
      if (TranspId != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(TranspId);
      }
      if (TranspLength != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(TranspLength);
      }
      if (RoAxisDirection != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(RoAxisDirection);
      }
      if (TranspRunTravel != 0F) {
        output.WriteRawTag(109);
        output.WriteFloat(TranspRunTravel);
      }
      if (RoAxisId != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(RoAxisId);
      }
      if (RoAxisRunRadius != 0F) {
        output.WriteRawTag(125);
        output.WriteFloat(RoAxisRunRadius);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LineId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(LineId);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (PrefabName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(PrefabName);
      }
      if (AnchorPoint.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(AnchorPoint);
      }
      if (MoveDirection != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(MoveDirection);
      }
      if (StatorLength != 0F) {
        output.WriteRawTag(53);
        output.WriteFloat(StatorLength);
      }
      if (StatorCount != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(StatorCount);
      }
      if (IsRemovable != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsRemovable);
      }
      if (IsArc != false) {
        output.WriteRawTag(72);
        output.WriteBool(IsArc);
      }
      if (TranspId != 0) {
        output.WriteRawTag(80);
        output.WriteInt32(TranspId);
      }
      if (TranspLength != 0F) {
        output.WriteRawTag(93);
        output.WriteFloat(TranspLength);
      }
      if (RoAxisDirection != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(RoAxisDirection);
      }
      if (TranspRunTravel != 0F) {
        output.WriteRawTag(109);
        output.WriteFloat(TranspRunTravel);
      }
      if (RoAxisId != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(RoAxisId);
      }
      if (RoAxisRunRadius != 0F) {
        output.WriteRawTag(125);
        output.WriteFloat(RoAxisRunRadius);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LineId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(LineId);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (PrefabName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(PrefabName);
      }
      if (AnchorPoint.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(AnchorPoint);
      }
      if (MoveDirection != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(MoveDirection);
      }
      if (StatorLength != 0F) {
        size += 1 + 4;
      }
      if (StatorCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(StatorCount);
      }
      if (IsRemovable != false) {
        size += 1 + 1;
      }
      if (IsArc != false) {
        size += 1 + 1;
      }
      if (TranspId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TranspId);
      }
      if (TranspLength != 0F) {
        size += 1 + 4;
      }
      if (RoAxisDirection != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RoAxisDirection);
      }
      if (TranspRunTravel != 0F) {
        size += 1 + 4;
      }
      if (RoAxisId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RoAxisId);
      }
      if (RoAxisRunRadius != 0F) {
        size += 1 + 4;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ViewLineConfig other) {
      if (other == null) {
        return;
      }
      if (other.LineId != 0) {
        LineId = other.LineId;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.PrefabName.Length != 0) {
        PrefabName = other.PrefabName;
      }
      if (other.AnchorPoint.Length != 0) {
        AnchorPoint = other.AnchorPoint;
      }
      if (other.MoveDirection != 0) {
        MoveDirection = other.MoveDirection;
      }
      if (other.StatorLength != 0F) {
        StatorLength = other.StatorLength;
      }
      if (other.StatorCount != 0) {
        StatorCount = other.StatorCount;
      }
      if (other.IsRemovable != false) {
        IsRemovable = other.IsRemovable;
      }
      if (other.IsArc != false) {
        IsArc = other.IsArc;
      }
      if (other.TranspId != 0) {
        TranspId = other.TranspId;
      }
      if (other.TranspLength != 0F) {
        TranspLength = other.TranspLength;
      }
      if (other.RoAxisDirection != 0) {
        RoAxisDirection = other.RoAxisDirection;
      }
      if (other.TranspRunTravel != 0F) {
        TranspRunTravel = other.TranspRunTravel;
      }
      if (other.RoAxisId != 0) {
        RoAxisId = other.RoAxisId;
      }
      if (other.RoAxisRunRadius != 0F) {
        RoAxisRunRadius = other.RoAxisRunRadius;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            LineId = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            PrefabName = input.ReadString();
            break;
          }
          case 34: {
            AnchorPoint = input.ReadString();
            break;
          }
          case 40: {
            MoveDirection = input.ReadInt32();
            break;
          }
          case 53: {
            StatorLength = input.ReadFloat();
            break;
          }
          case 56: {
            StatorCount = input.ReadInt32();
            break;
          }
          case 64: {
            IsRemovable = input.ReadBool();
            break;
          }
          case 72: {
            IsArc = input.ReadBool();
            break;
          }
          case 80: {
            TranspId = input.ReadInt32();
            break;
          }
          case 93: {
            TranspLength = input.ReadFloat();
            break;
          }
          case 96: {
            RoAxisDirection = input.ReadInt32();
            break;
          }
          case 109: {
            TranspRunTravel = input.ReadFloat();
            break;
          }
          case 112: {
            RoAxisId = input.ReadInt32();
            break;
          }
          case 125: {
            RoAxisRunRadius = input.ReadFloat();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            LineId = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            PrefabName = input.ReadString();
            break;
          }
          case 34: {
            AnchorPoint = input.ReadString();
            break;
          }
          case 40: {
            MoveDirection = input.ReadInt32();
            break;
          }
          case 53: {
            StatorLength = input.ReadFloat();
            break;
          }
          case 56: {
            StatorCount = input.ReadInt32();
            break;
          }
          case 64: {
            IsRemovable = input.ReadBool();
            break;
          }
          case 72: {
            IsArc = input.ReadBool();
            break;
          }
          case 80: {
            TranspId = input.ReadInt32();
            break;
          }
          case 93: {
            TranspLength = input.ReadFloat();
            break;
          }
          case 96: {
            RoAxisDirection = input.ReadInt32();
            break;
          }
          case 109: {
            TranspRunTravel = input.ReadFloat();
            break;
          }
          case 112: {
            RoAxisId = input.ReadInt32();
            break;
          }
          case 125: {
            RoAxisRunRadius = input.ReadFloat();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ViewLineConfigList : pb::IMessage<ViewLineConfigList>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ViewLineConfigList> _parser = new pb::MessageParser<ViewLineConfigList>(() => new ViewLineConfigList());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ViewLineConfigList> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::GRpc.SimulationProtocol.SimulationProtocolReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ViewLineConfigList() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ViewLineConfigList(ViewLineConfigList other) : this() {
      viewLineConfigs_ = other.viewLineConfigs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ViewLineConfigList Clone() {
      return new ViewLineConfigList(this);
    }

    /// <summary>Field number for the "viewLineConfigs" field.</summary>
    public const int ViewLineConfigsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::GRpc.SimulationProtocol.ViewLineConfig> _repeated_viewLineConfigs_codec
        = pb::FieldCodec.ForMessage(10, global::GRpc.SimulationProtocol.ViewLineConfig.Parser);
    private readonly pbc::RepeatedField<global::GRpc.SimulationProtocol.ViewLineConfig> viewLineConfigs_ = new pbc::RepeatedField<global::GRpc.SimulationProtocol.ViewLineConfig>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::GRpc.SimulationProtocol.ViewLineConfig> ViewLineConfigs {
      get { return viewLineConfigs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ViewLineConfigList);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ViewLineConfigList other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!viewLineConfigs_.Equals(other.viewLineConfigs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= viewLineConfigs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      viewLineConfigs_.WriteTo(output, _repeated_viewLineConfigs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      viewLineConfigs_.WriteTo(ref output, _repeated_viewLineConfigs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += viewLineConfigs_.CalculateSize(_repeated_viewLineConfigs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ViewLineConfigList other) {
      if (other == null) {
        return;
      }
      viewLineConfigs_.Add(other.viewLineConfigs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            viewLineConfigs_.AddEntriesFrom(input, _repeated_viewLineConfigs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            viewLineConfigs_.AddEntriesFrom(ref input, _repeated_viewLineConfigs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
