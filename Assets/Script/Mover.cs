using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GRpc.SimulationProtocol;
using UnityEngine;
using UnityEngine.UI;

public class Mover : MonoBehaviour
{
    public int id; // 用于标识物体
    public Line line; // 所在线体

    public float RealPos { get; private set; }

    AxisFeedback axisFeedback = new(); // 轴反馈

    // private LineConfig _config;
    public ViewLineConfig config;
    private Text _idText;
    private Text _posText;
    private SpriteRenderer _spriteRenderer;
    private Bounds _combinedBounds;

    void Start()
    {
        _combinedBounds = line.GetBounds();
        _spriteRenderer = GetComponent<SpriteRenderer>();

        // 缓存Text组件引用，避免在运行时查找
        Transform textPosTransform = transform.Find("text_pos");
        Transform textIdTransform = transform.Find("text_id");
        if (textPosTransform != null) _posText = textPosTransform.GetComponent<Text>();
        if (textIdTransform != null) _idText = textIdTransform.GetComponent<Text>();


        //StartCoroutine(CheckBoundaryCoroutine()); // 启动协程
    }

    // 缓存颜色，避免每帧创建新的Color对象
    private static readonly Color RunningColor = Color.green;
    private static readonly Color ErrorColor = Color.red;
    private static readonly Color IdleColor = Color.white;

    // 上一帧的位置和状态，用于减少不必要的更新
    private float _lastRealPos = -1f;
    private Color _lastColor = Color.white;
    private int _lastAxisCurObject = -1;

    // Update is called once per frame
    void Update()
    {
        // 只有当ID文本第一次设置或ID变化时才更新
        if (_idText != null && _idText.text != $"Id: {id}")
        {
            _idText.text = $"Id: {id}";
        }

        //运行状态的颜色变化 - 使用缓存的颜色对象
        Color newColor;
        if (axisFeedback.DiAxisCurVel > 10000f || axisFeedback.DiAxisCurVel < -10000f)
        {
            newColor = RunningColor;
        }
        else if (axisFeedback.UiAxisMotionErrCode != 0 && axisFeedback.UiAxisDrvErrCode != 0) //轴错误或者驱动错误状态
        {
            newColor = ErrorColor;
        }
        else
        {
            newColor = IdleColor;
        }

        // 只有当颜色发生变化时才更新
        if (newColor != _lastColor && _spriteRenderer != null)
        {
            _spriteRenderer.color = newColor;
            _lastColor = newColor;
        }
    }

    private IEnumerator CheckBoundaryCoroutine()
    {
        while (true) // 持续执行
        {
            yield return new WaitForEndOfFrame();
            CheckParentBoundary(); // 检查边界
            yield return new WaitForSeconds(0.1f); // 延迟0.1秒，降低频率
        }
    }


    private void ArcMove()
    {
        if (!config.IsArc) return;

        // 计算移动比例（限制在0-1之间）
        float moveRatio = Mathf.Clamp01(RealPos / config.TranspRunTravel);

        // 确定左右弧
        bool isLeft = line.name.Contains("Left");

        // 确定起始角度和旋转方向
        float startAngle = line.IsStartFromTop ? Mathf.PI / 2 : -Mathf.PI / 2;

        float angleRange = Mathf.PI;

        // 调整旋转方向变量
        float rotationDirection = isLeft ? -1 : 1;
        float currentAngle = line.IsStartFromTop
            ? startAngle - angleRange * moveRatio * rotationDirection
            : startAngle + angleRange * moveRatio * rotationDirection;

        // 计算圆弧坐标
        Vector3 newPos = line.ReferencePoint + new Vector3(
            config.RoAxisRunRadius * Mathf.Cos(currentAngle),
            config.RoAxisRunRadius * Mathf.Sin(currentAngle),
            0);

        transform.position = newPos;

        // 调整切线方向计算
        float tangentAngle = currentAngle + (false ? -1 : 1) * Mathf.PI / 2;
        transform.right = new Vector3(
            Mathf.Cos(tangentAngle),
            Mathf.Sin(tangentAngle),
            0);
    }

    /// <summary>
    /// 1/4圆弧的移动方法
    /// </summary>
    private void QuarterArcMove()
    {
        if (!config.IsArc) return;
        // 1/4圆周长
        float quarterCircleLength = Mathf.PI * config.RoAxisRunRadius / 2;

        //判断弧所处方位
        bool isLeftTop = line.name.Contains("LeftTop");
        bool isLeftBottom = line.name.Contains("LeftBottom");
        bool isRightTop = line.name.Contains("RightTop");
        bool isRightBottom = line.name.Contains("RightBottom");

        // 计算圆心偏移
        float radius = config.RoAxisRunRadius;
        float yOffset = (isLeftTop || isRightTop) ? -radius : radius;
        Vector3 centerOffset = new Vector3(0, yOffset, 0);
        Vector3 adjustedCenter = line.ReferencePoint + centerOffset;

        // 根据区域设置起始角度和结束角度（按照钟表方向定义）
        float startAngle = 0f;
        float endAngle = 0f;

        if (isLeftTop)
        {
            // 左上：12-9点钟方向（π/2到π）
            startAngle = Mathf.PI / 2; // 12点钟方向
            endAngle = Mathf.PI;      // 9点钟方向
        }
        else if (isRightBottom)
        {
            // 右下：6-3点钟方向（3π/2到2π）
            startAngle = 3 * Mathf.PI / 2; // 6点钟方向
            endAngle = 2 * Mathf.PI;     // 3点钟方向
        }
        else if (isLeftBottom)
        {
            // 左下：9-6点钟方向（π到3π/2）
            startAngle = Mathf.PI;     // 9点钟方向
            endAngle = 3 * Mathf.PI / 2; // 6点钟方向
        }
        else if (isRightTop)
        {
            // 右上：3-12点钟方向（0到π/2）
            startAngle = 0;           // 3点钟方向
            endAngle = Mathf.PI / 2;  // 12点钟方向
        }

        // 计算移动比例（限制在0-1之间）
        float moveRatio = Mathf.Clamp01(RealPos / config.TranspRunTravel);

        // 计算当前角度 - 在起始角度和结束角度之间插值
        float currentAngle = Mathf.Lerp(startAngle, endAngle, moveRatio);

        // 计算圆弧上的位置
        Vector3 newPos = adjustedCenter + new Vector3(
            radius * Mathf.Cos(currentAngle),
            radius * Mathf.Sin(currentAngle),
            0);

        transform.position = newPos;

        // 计算切线方向（垂直于半径方向）
        // 注意：角度+π/2得到切线方向
        float tangentAngle = currentAngle + Mathf.PI / 2;

        // 根据不同象限调整切线方向
        // if (isLeftTop || isRightBottom)
        // {
        //     tangentAngle = currentAngle - Mathf.PI / 2;
        // }

        transform.right = new Vector3(
            Mathf.Cos(tangentAngle),
            Mathf.Sin(tangentAngle),
            0);
    }



    /// <summary>
    /// 判断是否超出线体的边界
    /// 超出边界，销毁物体
    /// </summary>
    void CheckParentBoundary()
    {
        // 检查是否超出父级物体的边界
        if (transform.position.x < _combinedBounds.min.x || transform.position.x > _combinedBounds.max.x ||
            transform.position.y < _combinedBounds.min.y || transform.position.y > _combinedBounds.max.y)
        {
            line.RemoveMover(id); // 超出边界，销毁物体
        }
        // 弧形线的边界判断
    }

    public void UpdatePos(AxisFeedback axisFeedback)
    {
        this.axisFeedback = axisFeedback;
        float newRealPos = axisFeedback.DiAxisCurPos / 1000f; //转换成mm
        if (newRealPos < 0) return;

        // 只有当位置发生变化时才更新位置
        if (Mathf.Abs(newRealPos - _lastRealPos) > 0.001f || _lastAxisCurObject != axisFeedback.IAxisCurObject)
        {
            RealPos = newRealPos;
            _lastRealPos = newRealPos;
            _lastAxisCurObject = axisFeedback.IAxisCurObject;

            UpdatePosition();
        }
    }

    /// <summary>
    /// 根据当前RealPos更新动子位置
    /// </summary>
    private void UpdatePosition()
    {
        if (config == null) return;
        // 直线段移动
        if (!config.IsArc)// (axisFeedback.IAxisCurObject == 0)//0-直线段 1-弧形段
        {
            transform.position = line.ReferencePoint + line.Direction * RealPos;
        }
        else  //弧形段
        {
            // 根据配置选择移动方式
            if (config.PrefabName == "arc_1150")
            {
                QuarterArcMove();
            }
            else
            {
                ArcMove();
            }
        }

        // 只有当位置文本存在时才更新
        if (_posText != null)
        {
            _posText.text = $"{RealPos:F3}";
        }
    }

    /// <summary>
    /// 强制更新动子位置，用于导轨位置变化后立即更新动子位置
    /// </summary>
    public void ForceUpdatePosition()
    {
        UpdatePosition();
    }
}