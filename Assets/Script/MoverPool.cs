using System.Collections.Generic;
using UnityEngine;

public class MoverPool
{
    private List<Mover> availableMovers = new();
    private GameObject moverPrefab;


    public MoverPool(GameObject prefab, int initialSize = 0)
    {
        moverPrefab = prefab;
        if (initialSize == 0) return;
        for (int i = 0; i < initialSize; i++)
        {
            CreateMover(i);
        }
    }

    private void CreateMover(int id)
    {
        GameObject moverObject = Object.Instantiate(moverPrefab);
        Mover mover = moverObject.GetComponent<Mover>();
        mover.id = id;
        mover.gameObject.SetActive(false); // 初始时不激活
        availableMovers.Add(mover);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <param name="active">默认不激活</param>
    /// <returns></returns> <summary>
    public Mover GetMover(int id, bool active = false)
    {
        if (availableMovers.Count > 0)
        {
            Mover mover = availableMovers[availableMovers.Count - 1];
            availableMovers.RemoveAt(availableMovers.Count - 1);
            mover.gameObject.SetActive(active); // 激活对象
            return mover;
        }
        else
        {
            CreateMover(id); // 如果没有可用的，则创建新的
            return GetMover(id); // 递归调用返回新的Mover
        }
    }

    public void ReleaseMover(Mover mover)
    {
        if (mover == null) return;
        mover.gameObject.SetActive(false); // 禁用对象
        availableMovers.Add(mover); // 返回到池中
    }
}

