using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GRpc.SimulationProtocol;
using Unity.VisualScripting;
using UnityEngine;

/// <summary>
/// 整个线体的单例管理类
/// </summary>
public class ViewManager : MonoBehaviour
{

    public List<Line> Lines { get; private set; } //上下左右线体集合
    public MoverPool moverPool;
    private Canvas canvas;

    // public LineConfig lineConfig;

    public List<ViewLineConfig> ViewLineConfigs { get; set; }

    public List<StationConfig> StationConfigs { get; set; }

    public static ViewManager Instance { get; private set; }

    private void Awake()
    {
        Instance = this;
        canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            Debug.LogError("Canvas not found in scene");
        }
    }


    // Start is called before the first frame update
    void Start()
    {
        InitlalizeLines();
        InitializeMoverPool();
        // TODO 初始化工位
        //InitlalizeStations();
    }

    // 缓存线体ID到线体对象的映射，避免每次都使用LINQ查询
    private Dictionary<int, Line> _lineIdMap = new Dictionary<int, Line>();

    /// <summary>
    /// 初始化线体ID映射表
    /// </summary>
    private void InitializeLineIdMap()
    {
        _lineIdMap.Clear();
        foreach (var line in Lines)
        {
            if (!_lineIdMap.ContainsKey(line.Id))
            {
                _lineIdMap.Add(line.Id, line);
            }
        }
    }

    /// <summary>
    /// 动态增减动子 - 性能优化版本
    /// </summary>
    /// <param name="feedbacks"></param>
    private void UpdateMovers(AxisFeedbackList feedbacks)
    {
        if (feedbacks is null || feedbacks.AxisFeedbacks.Count == 0) return;

        // 确保线体ID映射表已初始化
        if (_lineIdMap.Count == 0 && Lines.Count > 0)
        {
            InitializeLineIdMap();
        }

        foreach (var feedback in feedbacks.AxisFeedbacks)
        {
            // 使用字典查找而不是LINQ查询
            if (!_lineIdMap.TryGetValue(feedback.IAxisCurObjectID, out Line line))
            {
                continue; // 如果找不到对应的线体，跳过此反馈
            }

            var mover = line.GetMover(feedback.IAxisID);
            if (mover is null)
            {
                // 在其他线体中查找并移除动子
                foreach (var l in Lines)
                {
                    // l.RemoveMover(feedback.IAxisID);
                    // break; // 找到并移除后立即退出循环

                    var existingMover = l.GetMover(feedback.IAxisID);
                    if (existingMover != null)
                    {
                        l.RemoveMover(existingMover);
                        break; // 找到并移除后立即退出循环
                    }
                }

                line.AddMover(feedback.IAxisID);
                mover = line.GetMover(feedback.IAxisID); // 获取新添加的动子
            }

            // 确保动子已激活并更新反馈
            if (mover != null)
            {
                if (!mover.isActiveAndEnabled) mover.gameObject.SetActive(true);
                mover.UpdatePos(feedback);
            }
        }
    }

    /// <summary>
    /// 直接更新接驳段导轨位置
    /// </summary>
    /// <param name="feedbacks"></param>
    private void UpdateRoAxises(RoAxisFeedbackList feedbacks)
    {
        // 创建需要更新动子位置的线体列表
        List<Line> updatedLines = new();

        for (int i = 0; i < feedbacks.RoAxisFeedbacks.Count; i++)
        {
            var feedback = feedbacks.RoAxisFeedbacks[i];
            var line = Lines.FirstOrDefault(l => l.LineConfig.TranspId == i);
            if (line != null)
            {
                line.UpdateRoPos(feedback, i);
                if (line.LineConfig.IsRemovable)
                {
                    updatedLines.Add(line);
                }
            }

            var roLine = Lines.FirstOrDefault(l => l.LineConfig.RoAxisId == i);
            roLine?.UpdateRoPos(feedback, i); // 更新旋转轴导轨位置
        }

        // 更新受影响线体上的所有动子位置
        foreach (var line in updatedLines)
        {
            foreach (var mover in line.movers)
            {
                if (mover != null && mover.isActiveAndEnabled)
                {
                    // 强制更新动子位置，确保使用最新的参考点
                    mover.ForceUpdatePosition();
                }
            }
        }
    }



    private void InitlalizeLines()
    {
        //清除所有现有Line对象
        var existingLines = FindObjectsOfType<Line>();
        foreach (var line in existingLines)
        {
            Destroy(line.gameObject);
        }
        Lines = new List<Line>();
        foreach (var config in ViewLineConfigs)
        {
            var anchorPoint = new Vector3(float.Parse(config.AnchorPoint.Split(',')[0]), float.Parse(config.AnchorPoint.Split(',')[1]), float.Parse(config.AnchorPoint.Split(',')[2]));
            var lineObj = Instantiate(Resources.Load("Prefab/line") as GameObject, anchorPoint, Quaternion.identity, canvas != null ? canvas.transform : transform);
            var line = lineObj.GetComponent<Line>();
            line.Initlalize(config);
            line.transform.position = anchorPoint;
            Lines.Add(line);
            Debug.Log($"Initialized Line {config.Name} with ID {config.LineId}");
        }
        //绑定动子移动事件
        ComClientManager.Instance.OnAxisFeedbackReceived += UpdateMovers;
        //绑定旋转轴导轨移动事件
        ComClientManager.Instance.OnRoAxisFeedbackReceived += UpdateRoAxises;

        //自动调整相机视角
        CameraManager.Instance.AutoAdjustCamera();
    }

    private void InitializeMoverPool()
    {

        GameObject moverPrefab = ViewLineConfigs.Any(c => c.IsArc) ?
            Resources.Load("Prefab/mover_190") as GameObject :
            Resources.Load("Prefab/mover_410") as GameObject;
        moverPool = new MoverPool(moverPrefab); // 初始化对象池
    }
    /// <summary>
    /// 初始化工位
    /// </summary>
    private void InitlalizeStations()
    {
        if (StationConfigs is null || StationConfigs.Count == 0) return;
        foreach (var config in StationConfigs)
        {
            var line = Lines.First(l => l.Id == config.IOnObjID);
            line.AddStation(config);
        }
    }


}