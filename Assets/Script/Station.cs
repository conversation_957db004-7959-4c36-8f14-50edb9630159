using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GRpc.SimulationProtocol;
using UnityEngine;
using UnityEngine.UI;


public class Station : MonoBehaviour
{
    private SpriteRenderer _spriteRenderer;

    public StationConfig Config { get; private set; }

    public Line Line { get; private set; } // 所在线体

    // Start is called before the first frame update
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {
        transform.position = Line.ReferencePoint + Line.Direction * (float)Config.LrStationPos;
        _spriteRenderer.color = Config.IStnEnable == 1 ? Color.green : Color.gray;
    }

    public void SetStation(StationConfig config)
    {
        Config = config;
        Line = ViewManager.Instance.Lines.FirstOrDefault(l => l.Id == Config.IOnObjID);
        _spriteRenderer = GetComponent<SpriteRenderer>();
        var stationId = this.GetComponentInChildren<Text>();
        stationId.text = $"Stn{Config.IStationID}";
    }

    public Bounds GetBounds()
    {
        if (_spriteRenderer != null)
        {
            var bounds = _spriteRenderer.bounds;
            if (transform.parent != null)
            {
                bounds.center = transform.parent.TransformPoint(bounds.center);
            }
            return bounds;
        }
        return new Bounds(transform.position, Vector3.zero);
    }
}
