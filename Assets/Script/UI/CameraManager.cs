using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

public class CameraManager : MonoBehaviour
{
    public float zoomSpeed; // 缩放速度
    public float dragSpeed; // 拖拽速度

    private Camera mainCamera;

    public static CameraManager Instance { get; private set; }
    private void Awake()
    {
        Instance = this;
    }

    void Start()
    {
        mainCamera = FindFirstObjectByType<Camera>(); // 找到第一个摄像机对象

        if (mainCamera == null)
        {
            Debug.LogError("No main camera found. Please ensure there is a main camera in the scene.");
            return;
        }
    }

    void Update()
    {
        // 确保主相机存在
        if (mainCamera == null) return;

        ZoomCameraView();
        DragCameraView();
    }

    private void ZoomCameraView()
    {
        // 检测Ctrl键是否被按下
        if (Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl))
        {
            // 获取鼠标滚轮的输入
            float scrollInput = Input.GetAxis("Mouse ScrollWheel");

            if (scrollInput != 0)
            {
                // 获取鼠标所在屏幕位置并转换为世界坐标
                Vector3 mousePos = Input.mousePosition;
                Vector3 worldPositionBeforeZoom = mainCamera.ScreenToWorldPoint(new Vector3(mousePos.x, mousePos.y, mainCamera.nearClipPlane));

                // 执行缩放
                if (mainCamera.orthographic)
                {
                    mainCamera.orthographicSize -= scrollInput * zoomSpeed;
                    // mainCamera.orthographicSize = Mathf.Clamp(mainCamera.orthographicSize, minZoom, maxZoom);
                }
                else
                {
                    mainCamera.fieldOfView -= scrollInput * zoomSpeed;
                    // mainCamera.fieldOfView = Mathf.Clamp(mainCamera.fieldOfView, minZoom, maxZoom);
                }

                // 缩放后再次计算鼠标的世界坐标
                Vector3 worldPositionAfterZoom = mainCamera.ScreenToWorldPoint(new Vector3(mousePos.x, mousePos.y, mainCamera.nearClipPlane));

                // 计算相机需要移动的位移
                Vector3 offset = worldPositionBeforeZoom - worldPositionAfterZoom;

                // 调整相机位置
                mainCamera.transform.position += offset;
            }
        }

    }

    private void DragCameraView()
    {
        // 检测右键是否被按下
        if (Input.GetMouseButton(1))
        {
            // 获取鼠标在屏幕上的移动
            float mouseX = Input.GetAxis("Mouse X");
            float mouseY = Input.GetAxis("Mouse Y");

            // 计算相机的移动
            Vector3 moveDirection = new Vector3(-mouseX * dragSpeed, -mouseY * dragSpeed, 0);

            // 将屏幕上的移动转换为世界坐标中的移动
            Vector3 worldMoveDirection = mainCamera.transform.TransformDirection(moveDirection);

            // 调整相机位置
            mainCamera.transform.position += worldMoveDirection;
        }
    }

    /// <summary>
    /// 获取场景中所有物体的总包围盒
    /// </summary>
    private Bounds GetAllObjectsBounds()
    {
        Bounds totalBounds = new Bounds();
        bool hasBounds = false;

        // 收集所有Line对象
        Line[] lines = FindObjectsOfType<Line>();
        foreach (Line line in lines)
        {
            Bounds lineBounds = line.GetBounds();
            if (!hasBounds)
            {
                totalBounds = lineBounds;
                hasBounds = true;
            }
            else
            {
                totalBounds.Encapsulate(lineBounds);
            }
        }

        return totalBounds;
    }

    /// <summary>
    /// 动态调整相机视角
    /// </summary>
    /// <param name="paddingFactor">留白因子</param>
    public void AutoAdjustCamera(float paddingFactor = 0.4f)
    {
        if (mainCamera == null) return;

        Bounds totalBounds = GetAllObjectsBounds();
        if (totalBounds.size == Vector3.zero) return;

        // 计算相机位置
        Vector3 centerPosition = new Vector3(totalBounds.center.x, totalBounds.center.y, mainCamera.transform.position.z);
        mainCamera.transform.position = centerPosition;

        // 计算相机视野
        float objectWidth = totalBounds.size.x * paddingFactor;
        float objectHeight = totalBounds.size.y * paddingFactor;

        if (mainCamera.orthographic)
        {
            // 正交相机调整
            float orthographicSize = Mathf.Max(objectWidth, objectHeight);
            mainCamera.orthographicSize = orthographicSize;
        }
        else
        {
            // 透视相机调整
            float fov = mainCamera.fieldOfView;
            float aspect = mainCamera.aspect;

            // 计算所需距离
            float distanceX = objectWidth / Mathf.Tan(fov * 0.5f * Mathf.Deg2Rad * aspect);
            float distanceY = objectHeight / Mathf.Tan(fov * 0.5f * Mathf.Deg2Rad);
            float distance = Mathf.Max(distanceX, distanceY);

            // 设置相机位置为Z轴负方向
            mainCamera.transform.position = new Vector3(centerPosition.x, centerPosition.y, -distance);
        }
    }


}

