using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GRpc.SimulationProtocol;
using UnityEngine;
using UnityEngine.UI;

public class Line : MonoBehaviour
{
    public List<Mover> movers = new();

    public List<Stator> stators = new();

    private Bounds? cachedBounds = null; // 用于缓存第一次调用的结果

    public Vector3 Direction { get; private set; }


    /// <summary>
    /// 弧形端动子位置最小值在上方
    /// </summary>
    public bool IsStartFromTop { get; private set; } = false;

    /// <summary>
    /// 动子移动时的参照点,或弧形圆心的位置
    /// 根据是否面向编码器来确定
    /// </summary>
    public Vector3 ReferencePoint { get; private set; }

    /// <summary>
    /// 线体Id
    /// </summary>

    public int Id { get; private set; }

    public ViewLineConfig LineConfig { get; private set; }

    // private LineConfig _config;

    public void Initlalize(ViewLineConfig config)
    {
        LineConfig = config;
        Direction = LineConfig.MoveDirection switch
        {
            1 => Vector3.up,
            2 => Vector3.down,
            3 => Vector3.left,
            4 => Vector3.right,
            _ => Vector3.zero,
        };
        Id = LineConfig.LineId;
        name = LineConfig.Name;
        if (LineConfig.StatorLength >= 0)
        {
            //添加上下段线体定子预制体
            for (int i = 0; i < LineConfig.StatorCount; i++)
            {
                if (LineConfig.IsArc)
                {
                    var isStartFromTop = LineConfig.MoveDirection == 2;//从上到下移动
                    this.AddArc(this.transform.position, isStartFromTop);
                }
                else
                {
                    this.AddStator(new Vector3(this.transform.position.x + LineConfig.StatorLength * i, this.transform.position.y, 0), LineConfig.StatorLength);
                }
            }
        }
    }

    // 缓存定子引用，避免每帧重复查询
    private Stator _firstStator;
    private Stator _lastStator;

    void Start()
    {
        // 在Start中缓存定子引用
        if (stators.Count > 0)
        {
            _firstStator = stators[0];
            _lastStator = stators[stators.Count - 1];
        }

        // 初始化参考点
        UpdateReferencePoint();
    }

    void Update()
    {
        // 只有在需要时才更新参考点（例如当定子位置变化时）
        if (LineConfig.IsRemovable)
        {
            UpdateReferencePoint();


            // _firstStator.SetPos(realPos);
        }
    }

    /// <summary>
    /// 更新参考点位置，从Update中提取出来以提高性能
    /// </summary>
    private void UpdateReferencePoint()
    {
        if (LineConfig == null) return;
        // 确定是否为上导轨或下导轨
        bool isUp = LineConfig.Name.Contains("Up");
        bool isDown = LineConfig.Name.Contains("Down");
        bool isLeftCenter = LineConfig.Name.Contains("LeftCenter");
        bool isRightCenter = LineConfig.Name.Contains("RightCenter");
        bool isLineMiddle2 = LineConfig.Name == "LineMiddle2";//日本线数据反馈的bug
        #region 动态调整参考点(移动的最小端)
        if (LineConfig.IsArc && LineConfig.PrefabName == "arc_311")
        {
            if (!isUp && !isDown)
            {
                return;
            }

            // 使用缓存的定子引用
            Stator stator = isUp ? _lastStator : _firstStator;
            if (stator == null) return;

            Vector3 offset = stator.transform.position;

            // 根据上导轨或下导轨设置偏移量
            if (isUp)
            {
                offset.x += stator.Width; // 参考点在最后一段导轨的右侧
            }

            if (isDown)
            {
                Direction = Vector3.right;
            }

            // 更新参考点并立即应用
            Vector3 oldReferencePoint = ReferencePoint;
            ReferencePoint = offset;

            // 如果参考点发生了显著变化，记录日志
            if (Vector3.Distance(oldReferencePoint, ReferencePoint) > 0.01f)
            {
                Debug.Log($"Arc ReferencePoint updated: {oldReferencePoint} -> {ReferencePoint}");
            }
            return;
        }

        // 使用缓存的定子引用
        Stator selectedStator = Direction == Vector3.left ? _lastStator : _firstStator;
        if (selectedStator == null) return;

        Vector3 newOffset = selectedStator.transform.position;

        //日本线旋转接驳段方向会变动
        if (isLineMiddle2)
        {
            if (newOffset.y < 0)
            {
                Direction = Vector3.right;
            }
            else
            {
                Direction = Vector3.left;
            }
        }

        if (Direction == Vector3.left)
        {
            newOffset.x += selectedStator.Width; // 参考点在最后一段导轨的右侧
        }

        //右侧纵向磁驱段的参考点在下
        if (isRightCenter)
        {
            newOffset.y -= selectedStator.Width;
        }

        // 更新参考点并立即应用
        Vector3 oldRefPoint = ReferencePoint;
        ReferencePoint = newOffset;

        // 如果参考点发生了显著变化，记录日志
        if (Vector3.Distance(oldRefPoint, ReferencePoint) > 0.01f)
        {
            Debug.Log($"Linear ReferencePoint updated: {oldRefPoint} -> {ReferencePoint}");
        }
        #endregion
    }

    /// <summary>
    /// 从对象池获取Mover并添加到Line中
    /// </summary>
    /// <param name="moverPool"></param>
    /// <param name="id"></param>
    public void AddMover(int id)
    {
        Mover mv = ViewManager.Instance.moverPool.GetMover(id); // 从对象池获取Mover
        mv.line = this;
        mv.config = LineConfig;
        mv.id = id;
        mv.transform.parent = this.transform; // 将 mv 的父对象设置为当前 Line 游戏对象
        // 将物体恢复成水平状态，日本线特殊处理：纵向接驳不需要水平
        if (LineConfig.Name != "LineLeftCenter" && LineConfig.Name != "LineRightCenter")
        {
            mv.transform.eulerAngles = new Vector3(0, 0, 0);
        }

        if (LineConfig.Name.Contains("LineUp"))
        {
            //旋转180度
            mv.transform.Rotate(0, 0, 180);
            // 翻转mv内的子物体
            FlipChildObjects(mv.transform);
        }
        movers.Add(mv);
    }

    /// <summary>
    /// 翻转指定Transform对象下的所有子物体的旋转角度
    /// 主要用于上方线体字体的翻转
    /// </summary>
    /// <param name="parentTransform">父对象的Transform</param>
    private void FlipChildObjects(Transform parentTransform)
    {
        // 遍历父对象下的所有子对象
        for (int i = 0; i < parentTransform.childCount; i++)
        {
            Transform child = parentTransform.GetChild(i);
            child.transform.Rotate(0, 0, 180);
        }
    }


    /// <summary>
    /// 移除Mover并将其返回到对象池
    /// </summary>
    /// <param name="id"></param>
    public void RemoveMover(int id)
    {
        var mv = movers.Find(m => m.id == id);
        mv.line = null;
        movers.Remove(mv);
        ViewManager.Instance.moverPool.ReleaseMover(mv); // 将对象返回到对象池
    }

    /// <summary>
    /// 移除Mover并将其返回到对象池
    /// </summary>
    /// <param name="mv"></param>
    public void RemoveMover(Mover mv)
    {
        mv.line = null;
        movers.Remove(mv);
        if (LineConfig.Name.Contains("LineUp")) FlipChildObjects(mv.transform);
        ViewManager.Instance.moverPool.ReleaseMover(mv); // 将对象返回到对象池
    }

    public Mover GetMover(int mvId)
    {
        return movers.Find(mv => mv.id == mvId);
    }


    private Stator AddStator(Vector3 position, float width)
    {
        GameObject stPrefab = Resources.Load($"Prefab/{LineConfig.PrefabName.ToLower()}") as GameObject;
        var stator = Instantiate(stPrefab, position, Quaternion.identity).GetComponent<Stator>();
        if (position.y < 0) //下导轨需要镜像
        {
            stator.transform.localScale = new Vector3(1, -1, 1);
        }
        if (LineConfig.MoveDirection == 1 || LineConfig.MoveDirection == 2) //上下移动的导轨需要旋转90度
        {
            //以position为圆心顺时针旋转90度
            stator.transform.RotateAround(position, Vector3.back, 90);
        }
        stator.Direction = this.LineConfig.RoAxisDirection == 1 ? Vector3.up : Vector3.down;
        stator.Width = width;
        stator.transform.SetParent(this.transform);
        stators.Add(stator);
        // if (LineConfig.IsRemovable && !LineConfig.IsArc)//接驳段导轨移动
        // {
        //     ComClientManager.Instance.OnRoAxisFeedbackReceived += UpdateRoPos;
        // }
        return stator;
    }

    /// <summary>
    /// 添加弧形导轨
    /// </summary>
    /// <param name="position"></param>
    /// <param name="isStartFromTop">弧形端动子位置最小值在上方</param>
    /// <returns></returns>
    private Stator AddArc(Vector3 position, bool isStartFromTop = true)
    {
        GameObject st = Resources.Load($"Prefab/{LineConfig.PrefabName.ToLower()}") as GameObject;
        var stator = Instantiate(st, position, Quaternion.identity).GetComponent<Stator>();

        if (position.y < 0) //下导轨以X轴为中心翻转
        {
            stator.transform.localScale = new Vector3(1, -1, 1);
        }
        if (position.x > 0) //右边的以y轴为中心翻转
        {
            stator.transform.localScale = new Vector3(stator.transform.localScale.x * -1, stator.transform.localScale.y, stator.transform.localScale.z * -1);
        }
        IsStartFromTop = isStartFromTop;
        ReferencePoint = position; //圆心是弧形导轨定位点
        stator.transform.parent = this.transform;
        // IsArc = true;
        stators.Add(stator);
        return stator;
    }

    // 缓存预制体引用，避免重复加载
    private static GameObject _stationPrefab;

    /// <summary>
    /// 添加工位
    /// </summary>
    /// <param name="config"></param>
    public void AddStation(StationConfig config)
    {
        int statorIndex = (config.LrStationPos < this.LineConfig.StatorLength) ? 0 : (int)(config.LrStationPos / this.LineConfig.StatorLength);
        if (statorIndex >= stators.Count) return;
        var stator = stators[statorIndex];
        //相对于导轨的位置
        double pos = (config.LrStationPos < this.LineConfig.StatorLength) ? config.LrStationPos : config.LrStationPos % this.LineConfig.StatorLength;

        // 使用缓存的预制体引用
        if (_stationPrefab == null)
        {
            _stationPrefab = Resources.Load<GameObject>("Prefab/station");
        }

        var station = Instantiate(_stationPrefab, Vector3.zero, Quaternion.identity).GetComponent<Station>();
        station.SetStation(config);
    }

    /// <summary>
    /// 获取线体的 SpriteRenderer 以确定边界
    /// </summary>
    /// <returns></returns>
    public Bounds GetBounds()
    {
        // 如果已缓存结果，直接返回
        if (cachedBounds.HasValue)
        {
            return cachedBounds.Value;
        }

        // 初始化边界
        Bounds combinedBounds = new Bounds();
        bool firstStator = true;

        // 直接使用缓存的stators列表而不是每次都遍历transform
        foreach (Stator stator in stators)
        {
            SpriteRenderer statorRenderer = stator.GetComponent<SpriteRenderer>();
            if (statorRenderer)
            {
                if (firstStator)
                {
                    // 如果是第一个 stator，直接使用它的边界
                    combinedBounds = statorRenderer.bounds;
                    firstStator = false;
                }
                else
                {
                    // 否则，扩展 combinedBounds 以包含当前 stator 的边界
                    combinedBounds.Encapsulate(statorRenderer.bounds);
                }
            }
        }

        // 缓存结果
        cachedBounds = combinedBounds;
        return combinedBounds;
    }

    /// <summary>
    /// 直接更新接驳段导轨位置
    /// </summary>
    /// <param name="feedbacks"></param>
    private void UpdateRoPos(RoAxisFeedbackList feedbacks)
    {
        if ((feedbacks is null) || (feedbacks.RoAxisFeedbacks.Count < 1)) return;
        var length = LineConfig.TranspLength;
        var realPos = length * Math.Abs(feedbacks.RoAxisFeedbacks[LineConfig.TranspId].LrActPosition) / LineConfig.TranspLength;

        // 使用缓存的第一个定子引用，避免每次都查询
        if (_firstStator != null)
        {
            var pos = (float)(realPos < 30 ? 0 : realPos);
            _firstStator.transform.localPosition = _firstStator.Direction * pos;
        }
        // 只有在需要时才更新参考点（例如当定子位置变化时）
        if (LineConfig.IsRemovable)
        {
            UpdateReferencePoint();
        }
    }

    public void UpdateRoPos(RoAxisFeedback feedback, int roId)
    {
        // 使用缓存的第一个定子引用，避免每次都查询
        if (_firstStator != null)
        {
            // TODO: 更新旋转轴
            if (LineConfig.RoAxisId == roId)
            {
                //transform.rotation = Quaternion.Euler(0, 0, (float)feedback.LrActPosition);
                return;
            }

            // 记录更新前的位置
            Vector3 oldPosition = _firstStator.transform.localPosition;
            bool isAbsolute = feedback.LrActPosition > 1000;
            float realPos = 0;
            float transpRunMin = 30;
            if (isAbsolute)
            {
                transpRunMin = 4476;
                // 绝对位置
                realPos = (float)(feedback.LrActPosition - transpRunMin) / (LineConfig.TranspRunTravel - transpRunMin) * LineConfig.TranspLength;
            }
            else
            {
                // 相对位置 
                realPos = (float)(LineConfig.TranspLength * Math.Abs(feedback.LrActPosition) / LineConfig.TranspRunTravel);
            }
            var pos = (float)(realPos < 30 ? 0 : realPos);
            _firstStator.transform.localPosition = _firstStator.Direction * pos;

            // 记录位置变化
            Vector3 newPosition = _firstStator.transform.localPosition;
            if (Vector3.Distance(oldPosition, newPosition) > 0.01f)
            {
                Debug.Log($"Stator position updated: {oldPosition} -> {newPosition}, RoAxis: {feedback.LrActPosition}");
            }

            // 记录更新前的参考点
            Vector3 oldReferencePoint = ReferencePoint;

            // 更新参考点，确保动子位置与导轨位置同步
            if (LineConfig.IsRemovable)
            {
                UpdateReferencePoint();

                // 记录参考点变化
                if (Vector3.Distance(oldReferencePoint, ReferencePoint) > 0.01f)
                {
                    Debug.Log($"ReferencePoint updated in UpdateRoPos: {oldReferencePoint} -> {ReferencePoint}");
                }
            }



        }
    }
}
